/**
 * 分类管理页面 - 重构版本
 * 支持单页面应用的动态加载和事件管理
 *
 * 设计原则：
 * 1. 兼容AdminFramework的页面管理机制
 * 2. 提供可靠的DOM元素检测和事件绑定
 * 3. 支持页面的动态加载和卸载
 * 4. 确保事件监听器的正确绑定和清理
 */

(function(window) {
    'use strict';

    // 全局变量
    let categories = [];
    let categoryToDelete = null;
    let isPageInitialized = false;
    let eventListenersAttached = false;
    let initializationTimer = null;

    /**
     * 为AdminFramework提供的初始化函数
     * 这个函数会被AdminFramework在页面加载时调用
     */
    window.initializeCategoryPageForFramework = function() {
        console.log('AdminFramework调用分类页面初始化');
        initializeCategoryPage();
    };

    /**
     * 主初始化函数
     */
    function initializeCategoryPage() {
        if (isPageInitialized) {
            console.log('分类页面已经初始化，跳过重复初始化');
            return;
        }

        console.log('开始初始化分类页面');

        // 使用可靠的DOM检测机制
        waitForDOMElements(() => {
            try {
                // 标记页面已初始化
                isPageInitialized = true;

                // 添加页面加载动画
                setupPageAnimation();

                // 初始化事件监听器
                initializeEventListeners();

                // 获取分类数据
                fetchCategories();

                // 阻止表单提交事件
                preventFormSubmission();

                console.log('分类页面初始化完成');

            } catch (error) {
                console.error('分类页面初始化失败:', error);
                isPageInitialized = false;
            }
        });
    }

    /**
     * 等待DOM元素加载完成
     */
    function waitForDOMElements(callback, maxAttempts = 50) {
        let attempts = 0;

        function checkElements() {
            attempts++;

            // 检查关键DOM元素是否存在
            const categoryContainer = document.getElementById('category-container');
            const categoryTableBody = document.getElementById('category-table-body');
            const categoryModal = document.getElementById('category-modal');
            const saveBtn = document.getElementById('save-btn');

            if (categoryContainer && categoryTableBody && categoryModal && saveBtn) {
                console.log(`DOM元素检测成功 (尝试次数: ${attempts})`);
                callback();
            } else if (attempts < maxAttempts) {
                console.log(`DOM元素检测中... (${attempts}/${maxAttempts})`);
                setTimeout(checkElements, 100);
            } else {
                console.error('DOM元素检测超时，部分功能可能不可用');
                console.log('缺失的元素:', {
                    categoryContainer: !!categoryContainer,
                    categoryTableBody: !!categoryTableBody,
                    categoryModal: !!categoryModal,
                    saveBtn: !!saveBtn
                });
                // 即使部分元素缺失，也尝试初始化
                callback();
            }
        }

        checkElements();
    }

    /**
     * 设置页面动画
     */
    function setupPageAnimation() {
        const container = document.getElementById('category-container');
        if (container) {
            container.style.opacity = '0';
            setTimeout(() => {
                container.style.transition = 'opacity 0.5s ease';
                container.style.opacity = '1';
            }, 100);
        }
    }

    /**
     * 阻止表单提交事件
     */
    function preventFormSubmission() {
        const categoryForm = document.getElementById('category-form');
        if (categoryForm) {
            categoryForm.addEventListener('submit', (event) => {
                console.log('表单提交事件被阻止');
                event.preventDefault();
                return false;
            });
        }
    }

    /**
     * 页面清理函数
     * 当页面卸载时调用，清理事件监听器和定时器
     */
    function cleanupCategoryPage() {
        console.log('清理分类页面');

        // 重置状态
        isPageInitialized = false;
        eventListenersAttached = false;

        // 清理定时器
        if (initializationTimer) {
            clearTimeout(initializationTimer);
            initializationTimer = null;
        }

        // 清理全局变量
        categories = [];
        categoryToDelete = null;
    }

    // 注册页面清理函数到AdminFramework
    if (window.AdminFramework) {
        window.AdminFramework.registerCleanupFunction('category', cleanupCategoryPage);
    }

    // 兼容性初始化 - 支持传统的页面加载方式
    document.addEventListener('DOMContentLoaded', function() {
        // 检查是否在AdminFramework环境中
        if (window.AdminFramework && window.AdminFramework.getCurrentRoute) {
            const currentRoute = window.AdminFramework.getCurrentRoute();
            if (currentRoute === 'category') {
                console.log('在AdminFramework环境中，等待框架调用初始化');
                return;
            }
        }

        // 传统页面加载方式的初始化
        console.log('传统页面加载方式初始化');
        initializeCategoryPage();
    });

    // 立即执行初始化检查（用于动态加载的情况）
    (function() {
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            // 延迟执行，确保AdminFramework有机会接管
            setTimeout(() => {
                if (!isPageInitialized) {
                    console.log('立即执行初始化检查');
                    initializeCategoryPage();
                }
            }, 200);
        }
    })();

    // 获取API基础URL
    function getApiBaseUrl() {
        // 根据当前URL推断API地址
        const currentLocation = window.location.href;
        const urlObj = new URL(currentLocation);
        // 使用相同域名，不要指定端口，使用相对路径
        return '';
    }

    // 发送API请求的标准函数
    async function sendApiRequest(operation, data, method = 'POST', maxRetries = 2) {

        try {
            // 添加特殊请求的日志
            if (operation === 'UpdateCategory') {
            } else if (operation === 'AddCategory') {
            }

            // 测试模式支持 - 如果在URL中有test=true参数，返回模拟数据
            if (window.location.search.includes('test=true') || window.location.hash.includes('test=true')) {

                // 模拟不同API的响应
                if (operation === 'GetCategoryList') {
                    return {
                        code: 200,
                        msg: "获取成功",
                        data: [
                            {
                                id: 'test-cat-1',
                                name: '测试一级分类1',
                                level: 1,
                                image: 'data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2250%22%20height%3D%2250%22%20viewBox%3D%220%200%2050%2050%22%3E%3Crect%20width%3D%2250%22%20height%3D%2250%22%20fill%3D%22%23ff7eb9%22%2F%3E%3Ctext%20x%3D%2225%22%20y%3D%2225%22%20font-family%3D%22Arial%2CSans%22%20font-size%3D%228%22%20text-anchor%3D%22middle%22%20alignment-baseline%3D%22middle%22%20fill%3D%22white%22%3E测试图片%3C%2Ftext%3E%3C%2Fsvg%3E',
                                parent_id: ""
                            },
                            {
                                id: 'test-cat-2',
                                name: '测试一级分类2',
                                level: 1,
                                image: "",
                                parent_id: ""
                            },
                            {
                                id: 'test-subcat-1',
                                name: '测试二级分类1',
                                level: 2,
                                image: "",
                                parent_id: 'test-cat-1'
                            }
                        ]
                    };
                } else if (operation === 'AddCategory') {
                    return {
                        code: 200,
                        id: 'new-cat-' + Date.now(),
                        data: [] // 返回完整分类列表
                    };
                } else if (operation === 'UpdateCategory') {
                    return {
                        code: 200,
                        msg: "更新分类成功",
                        data: [] // 返回完整分类列表
                    };
                } else if (operation === 'DeleteCategory') {
                    return {
                        code: 200,
                        msg: "删除分类成功",
                        data: [] // 返回完整分类列表
                    };
                }

                // 默认成功响应
                return { code: 200, msg: '测试模式响应' };
            }

            // 设置API端点和签名
            let apiEndpoint = '';
            let requestBody = {};

            // 根据操作类型设置不同的端点和请求体
            switch (operation) {
                case 'GetCategoryList':
                    apiEndpoint = '/api/get_category_list';
                    method = 'GET';
                    break;

                case 'AddCategory':
                    apiEndpoint = '/api/add_category';
                    // 准备请求数据
                    requestBody = { ...data };
                    // 添加签名
                    requestBody.sign = generateSignature(requestBody);
                    break;

                case 'UpdateCategory':
                    apiEndpoint = '/api/update_category';
                    // 准备请求数据
                    requestBody = { ...data };
                    // 添加签名
                    requestBody.sign = generateSignature(requestBody);
                    break;

                case 'DeleteCategory':
                    apiEndpoint = '/api/delete_category';
                    // 准备请求数据
                    requestBody = { ...data };
                    // 添加签名
                    requestBody.sign = generateSignature(requestBody);
                    break;

                default:
                    throw new Error(`未知的操作类型: ${operation}`);
            }

            // 设置请求超时控制
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

            try {
                let response;

                if (method === 'GET') {
                    response = await fetch(apiEndpoint, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json'
                        },
                        signal: controller.signal
                    });
                } else {
                    response = await fetch(apiEndpoint, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(requestBody),
                        signal: controller.signal
                    });
                }

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }

                const jsonResponse = await response.json();
                return jsonResponse;
            } catch (error) {
                if (error.name === 'AbortError') {
                    throw new Error('请求超时');
                }
                throw error;
            }
        } catch (error) {

            // 如果设置了重试次数且大于0，递归调用自身进行重试
            if (maxRetries > 0) {
                return sendApiRequest(operation, data, method, maxRetries - 1);
            }

            // 对于GetCategoryList操作，如果所有方法都失败，返回一个空的成功响应
            if (operation === 'GetCategoryList') {
                return {
                    code: 200,
                    msg: "获取成功(空数据)",
                    data: []
                };
            }

            throw new Error(`API请求失败: ${error.message}`);
        }
    }

    // 上传分类图片到服务器
    function uploadCategoryImageToServer(base64Data) {
        return new Promise((resolve, reject) => {
            try {
                console.log('开始上传分类图片，数据长度:', base64Data.length);
                
                // 准备上传数据
                const uploadData = {
                    image: base64Data
                };

                // 添加签名，包含错误处理
                try {
                    uploadData.sign = generateSignature(uploadData);
                    console.log('分类图片签名生成成功');
                } catch (signError) {
                    console.error('分类图片签名生成失败:', signError);
                    reject(new Error('签名生成失败: ' + signError.message));
                    return;
                }

                // 发送上传请求
                fetch('/api/upload_category_image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(uploadData),
                    timeout: 30000 // 30秒超时
                })
                .then(response => {
                    console.log('分类图片上传响应:', response.status);
                    
                    if (!response.ok) {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }
                    
                    return response.json();
                })
                .then(data => {
                    console.log('分类图片上传结果:', data);
                    
                    if (data.code === 200) {
                        resolve(data.data.image_url);
                    } else {
                        reject(new Error(data.msg || '图片上传失败'));
                    }
                })
                .catch(error => {
                    console.error('分类图片上传请求失败:', error);
                    
                    let errorMessage = '网络错误，上传失败';
                    if (error.message.includes('timeout')) {
                        errorMessage = '上传超时，请检查网络连接';
                    } else if (error.message.includes('413')) {
                        errorMessage = '图片文件过大，请选择较小的图片';
                    } else if (error.message.includes('500')) {
                        errorMessage = '服务器错误，请稍后重试';
                    }
                    
                    reject(new Error(errorMessage));
                });
            } catch (error) {
                console.error('uploadCategoryImageToServer: 意外错误:', error);
                reject(new Error('上传过程中发生意外错误: ' + error.message));
            }
        });
    }

    // 生成接口签名的函数
    function generateSignature(data) {
        try {
            console.log('分类页面签名生成开始');
            console.log('原始数据长度:', JSON.stringify(data).length);
            
            // 创建数据的副本（排除sign字段）
            const dataToSign = { ...data };
            if (dataToSign.sign) delete dataToSign.sign;
            
            // 获取所有键并按字母顺序排序
            const sortedKeys = Object.keys(dataToSign).sort();
            
            // 创建排序后的对象
            const sortedData = {};
            sortedKeys.forEach(key => {
                sortedData[key] = dataToSign[key];
            });
            
            // 转换为JSON字符串
            const jsonStr = JSON.stringify(sortedData);
            console.log('JSON字符串长度:', jsonStr.length);
            
            // 使用UTF-8编码
            const utf8Bytes = new TextEncoder().encode(jsonStr);
            console.log('UTF-8编码后的字节数组长度:', utf8Bytes.length);
            
            // 安全地将字节数组转换为字符串用于Base64编码
            // 使用分块处理避免reduce或apply的性能问题
            let utf8Str = '';
            const chunkSize = 8192; // 8KB chunks to avoid performance issues
            
            for (let i = 0; i < utf8Bytes.length; i += chunkSize) {
                const chunk = utf8Bytes.slice(i, i + chunkSize);
                utf8Str += String.fromCharCode.apply(null, chunk);
            }
            console.log('UTF-8字符串长度:', utf8Str.length);
            
            // 使用btoa进行Base64编码，添加错误处理
            let base64Str;
            try {
                base64Str = btoa(utf8Str);
                console.log('Base64编码后的字符串长度:', base64Str.length);
            } catch (base64Error) {
                console.error('Base64编码失败:', base64Error);
                throw new Error('Base64编码失败: ' + base64Error.message);
            }
            
            // 计算MD5哈希（使用外部MD5库）
            const md5Result = md5(base64Str);
            console.log('MD5签名结果:', md5Result);
            
            return md5Result;
        } catch (error) {
            console.error('generateSignature: 签名生成失败:', error);
            console.error('generateSignature: 错误堆栈:', error.stack);
            
            // 抛出更有意义的错误
            throw new Error('签名生成失败: ' + error.message);
        }
    }
    
    // 对象排序函数（用于调试）
    function sortObjectKeys(obj) {
        return Object.keys(obj).sort().reduce((result, key) => {
            result[key] = obj[key];
            return result;
        }, {});
    }

    // 渲染分类表格
    function renderCategoryTable(categoriesData) {
        const tableBody = document.getElementById('category-table-body');
        if (!tableBody) return;

        // 清空表格
        tableBody.innerHTML = '';

        // 如果没有分类数据，显示空状态
        if (!categoriesData || categoriesData.length === 0) {
            showEmptyState();
            return;
        }

        // 更新全局分类数组 - 先进行扁平化处理
        categories = flattenCategoryTree(categoriesData);

        // 分离一级分类和二级分类
        const parentCategories = categoriesData;
        
        // 渲染一级分类及其子分类
        parentCategories.forEach((category, index) => {
            const hasChildren = category.children && category.children.length > 0;
            const row = document.createElement('tr');
            
            // 设置行ID和数据属性
            row.id = `category-row-${category.id}`;
            row.setAttribute('data-id', category.id);
            row.setAttribute('data-level', category.level);
            row.classList.add('category-row');
            
            // 添加动画延迟效果
            row.style.opacity = '0';
            row.style.transform = 'translateY(10px)';
            row.style.transition = `all 0.3s ease ${index * 0.05}s`;
            
            // 计算此一级分类下的二级分类数量
            const childCount = hasChildren ? category.children.length : 0;
            
            // 创建行内容
            row.innerHTML = `
                <td class="id-column">
                    <div style="display: flex; align-items: center;">
                        <div class="toggle-subcategory-container">
                            <span class="toggle-subcategory" data-parent-id="${category.id}" title="点击展开/折叠二级分类" ${!hasChildren ? 'style="opacity:0.3"' : ''}>
                                <i class="fas fa-chevron-${hasChildren ? 'right' : 'down'}"></i>
                            </span>
                        </div>
                        <span style="margin-left: 5px;">${category.id || ''}</span>
                    </div>
                </td>
                <td class="image-column"><img src="${category.image || ''}" class="category-image" onerror="this.src='static/img/default-category.jpg'; this.onerror=null;"></td>
                <td class="name-column">
                    <strong>${category.name || '未命名分类'}</strong>
                    ${childCount > 0 ? `<span class="subcategory-count">${childCount}</span>` : ''}
                </td>
                <td class="level-column"><span class="category-level-badge level-1">一级分类</span></td>
                <td class="date-column">${formatDate(category.created_at || new Date())}</td>
                <td class="actions-column">
                    <div class="category-actions">
                        <button type="button" class="action-btn edit-btn" data-id="${category.id}" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="action-btn delete-btn" data-id="${category.id}" title="删除">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                        <button type="button" class="action-btn add-subcategory-btn" data-id="${category.id}" title="添加子分类">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </td>
            `;
            
            tableBody.appendChild(row);
            
            // 触发动画
            setTimeout(() => {
                row.style.opacity = '1';
                row.style.transform = 'translateY(0)';
            }, 10);
            
            // 渲染此一级分类下的所有二级分类
            if (hasChildren) {
                const childrenCount = category.children.length;
                
                category.children.forEach((subCategory, childIndex) => {
                    const subRow = document.createElement('tr');
                    
                    // 设置行ID和数据属性
                    subRow.id = `category-row-${subCategory.id}`;
                    subRow.setAttribute('data-id', subCategory.id);
                    subRow.setAttribute('data-level', subCategory.level);
                    subRow.setAttribute('data-parent-id', subCategory.parent_id);
                    subRow.classList.add('subcategory-row', `subcategory-${subCategory.parent_id}`);
                    
                    // 添加树形结构标记类
                    if (childrenCount === 1) {
                        // 如果只有一个子分类，添加单独标记
                        subRow.classList.add('tree-single');
                    } else {
                        // 多个子分类时，标记第一个和最后一个
                        if (childIndex === 0) {
                            subRow.classList.add('tree-start');
                        } else if (childIndex === childrenCount - 1) {
                            subRow.classList.add('tree-end');
                        }
                    }
                    
                    // 创建行内容
                    subRow.innerHTML = `
                        <td class="id-column">
                            <div style="display: flex; align-items: center;">
                                ${subCategory.id || ''}
                            </div>
                        </td>
                        <td class="image-column"><img src="${subCategory.image || ''}" class="category-image" onerror="this.src='static/img/default-category.jpg'; this.onerror=null;"></td>
                        <td class="name-column">${subCategory.name || '未命名分类'}</td>
                        <td class="level-column"><span class="category-level-badge level-2">二级分类</span></td>
                        <td class="date-column">${formatDate(subCategory.created_at || new Date())}</td>
                        <td class="actions-column">
                            <div class="category-actions">
                                <button type="button" class="action-btn edit-btn" data-id="${subCategory.id}" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="action-btn delete-btn" data-id="${subCategory.id}" title="删除">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        </td>
                    `;
                    
                    tableBody.appendChild(subRow);
                });
            }
        });

        // 更新分类计数
        updateCategoryCount();
        
        // 绑定编辑和删除按钮事件
        bindTableActions();
        
        // 绑定折叠/展开事件
        bindToggleEvents();
        
    }
    
    // 绑定折叠/展开按钮事件
    function bindToggleEvents() {
        const toggleButtons = document.querySelectorAll('.toggle-subcategory');
        
        toggleButtons.forEach(toggleBtn => {
            toggleBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const parentId = this.getAttribute('data-parent-id');
                const icon = this.querySelector('i');
                const parentRow = document.getElementById(`category-row-${parentId}`);
                const subRows = document.querySelectorAll(`.subcategory-${parentId}`);
                
                // 检查是否有子分类
                if (subRows.length === 0) {
                    
                    // 查找原始数据中是否有子分类
                    const parentCategory = findOriginalCategoryById(parentId);
                    if (parentCategory && parentCategory.children && parentCategory.children.length > 0) {
                        // 这种情况不应该发生，但如果发生了，提示用户刷新页面
                        showNotification('检测到数据不一致，请刷新页面', 'warning');
                        return;
                    }
                    
                    // 添加一个摇动效果提示没有子分类
                    this.style.animation = 'shake 0.5s ease';
                    setTimeout(() => {
                        this.style.animation = '';
                    }, 500);
                    
                    showNotification('该分类没有子分类', 'info');
                    return;
                }
                
                
                // 切换图标
                if (icon.classList.contains('fa-chevron-right')) {
                    icon.classList.replace('fa-chevron-right', 'fa-chevron-down');
                    parentRow.classList.add('expanded');
                    
                    // 显示子分类
                    subRows.forEach((row, index) => {
                        row.classList.add('visible');
                        
                        // 添加渐入动画
                        row.style.opacity = '0';
                        row.style.transform = 'translateY(-10px)';
                        setTimeout(() => {
                            row.style.transition = `all 0.3s ease ${index * 0.05}s`;
                            row.style.opacity = '1';
                            row.style.transform = 'translateY(0)';
                        }, 10);
                        
                    });
                } else {
                    icon.classList.replace('fa-chevron-down', 'fa-chevron-right');
                    parentRow.classList.remove('expanded');
                    
                    // 隐藏子分类
                    subRows.forEach(row => {
                        // 添加淡出动画
                        row.style.transition = 'all 0.2s ease';
                        row.style.opacity = '0';
                        row.style.transform = 'translateY(-10px)';
                        
                        // 等动画完成后移除可见类
                        setTimeout(() => {
                            row.classList.remove('visible');
                        }, 200);
                        
                    });
                }
            });
        });

        // 添加一个视觉提示，帮助用户注意到按钮
        setTimeout(() => {
            // 找到第一个有子分类的按钮进行闪烁提示
            const buttons = document.querySelectorAll('.toggle-subcategory');
            if (buttons.length > 0) {
                let foundButton = null;
                
                // 尝试找到一个有子分类的按钮
                for (let btn of buttons) {
                    const parentId = btn.getAttribute('data-parent-id');
                    const subRows = document.querySelectorAll(`.subcategory-${parentId}`);
                    if (subRows.length > 0) {
                        foundButton = btn;
                        break;
                    }
                }
                
                // 如果找到了，添加闪烁动画
                if (foundButton) {
                    foundButton.style.animation = 'pulse 1s infinite alternate';
                    // 定义脉冲动画
                    const style = document.createElement('style');
                    style.textContent = `
                        @keyframes pulse {
                            0% { color: #999999; }
                            100% { color: var(--primary-color); transform: scale(1.2); }
                        }
                        
                        @keyframes shake {
                            0%, 100% { transform: translateX(0); }
                            20%, 60% { transform: translateX(-3px); }
                            40%, 80% { transform: translateX(3px); }
                        }
                    `;
                    document.head.appendChild(style);
                    
                    // 5秒后停止动画
                    setTimeout(() => {
                        foundButton.style.animation = '';
                    }, 5000);
                }
            }
        }, 1000);
    }
    
    // 在原始(嵌套)数据中查找分类
    function findOriginalCategoryById(id) {
        const originalData = window.categoriesOriginalData || [];
        
        // 在一级分类中查找
        for (const category of originalData) {
            if (category.id === id) {
                return category;
            }
            
            // 在二级分类中查找
            if (category.children) {
                for (const subCategory of category.children) {
                    if (subCategory.id === id) {
                        return subCategory;
                    }
                }
            }
        }
        
        return null;
    }
    
    // 根据ID查找分类名称
    function findCategoryNameById(id) {
        const category = categories.find(cat => cat.id === id);
        return category ? category.name : '未知分类';
    }

    // 绑定表格操作按钮事件
    function bindTableActions() {
        
        // 使用事件委托绑定编辑按钮点击事件
        document.getElementById('category-table-body').addEventListener('click', function(e) {
            const editBtn = e.target.closest('.edit-btn');
            if (editBtn) {
                const categoryId = editBtn.getAttribute('data-id');
                if (categoryId) {
                    // 调用正确的编辑函数
                    const category = findCategoryById(categoryId);
                    if (category) {
                        openEditCategoryModal(category);
                    } else {
                        showNotification('未找到分类数据', 'error');
                    }
                }
            }
            
            const deleteBtn = e.target.closest('.delete-btn');
            if (deleteBtn) {
                const categoryId = deleteBtn.getAttribute('data-id');
                if (categoryId) {
                    const category = findCategoryById(categoryId);
                    if (category) {
                        openDeleteConfirmDialog(categoryId, category.name);
                    } else {
                        showNotification('未找到分类数据', 'error');
                    }
                }
            }

            const addSubcategoryBtn = e.target.closest('.add-subcategory-btn');
            if (addSubcategoryBtn) {
                const categoryId = addSubcategoryBtn.getAttribute('data-id');
                if (categoryId) {
                    openAddSubcategoryModal(categoryId);
                }
            }
        });
    }
    
    // 准备编辑分类
    function prepareEditCategory(categoryId) {
        
        // 查找分类对象
        const category = findCategoryById(categoryId);
        if (!category) {
            showNotification('未找到该分类', 'error');
            return;
        }
        
        // 填充编辑表单
        document.getElementById('edit-category-id').value = category.id;
        document.getElementById('edit-category-name').value = category.name || '';
        document.getElementById('edit-category-level').value = category.level || '1';
        
        // 处理父分类
        const parentSelect = document.getElementById('edit-parent-category');
        if (parentSelect) {
            if (category.level == 2 && category.parent_id) {
                populateParentCategoriesDropdown(parentSelect, category.parent_id);
            } else {
                populateParentCategoriesDropdown(parentSelect);
            }
        }
        
        // 显示当前图片
        const imagePreview = document.getElementById('edit-image-preview');
        if (imagePreview) {
            if (category.image) {
                imagePreview.src = category.image;
                imagePreview.style.display = 'block';
            } else {
                imagePreview.src = 'static/img/default-category.jpg';
                imagePreview.style.display = 'block';
            }
        }
        
        // 显示编辑模态框
        openModal(document.getElementById('edit-category-modal'));
    }
    
    // 编辑按钮点击处理函数
    function handleEditClick(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const button = this;
        const categoryId = button.getAttribute('data-id');
        
        // 获取分类对象
        const category = findCategoryById(categoryId);
        if (category) {
            // 调用正确的函数名
            openEditCategoryModal(category);
        } else {
            showNotification('未找到分类数据', 'error');
        }
    }
    
    // 删除按钮点击处理函数
    function handleDeleteClick(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const button = this;
        const categoryId = button.getAttribute('data-id');
        
        // 获取分类对象
        const category = findCategoryById(categoryId);
        if (category) {
            // 修改这里，使用正确的函数名
            openDeleteConfirmDialog(categoryId, category.name);
        } else {
            showNotification('未找到分类数据', 'error');
        }
    }

    // 格式化日期函数
    function formatDate(dateString) {
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return '-';
            
            return date.toISOString().split('T')[0];
        } catch (e) {
            return '-';
        }
    }

    // 查找父分类名称
    function findParentCategoryName(parentId) {
        if (!parentId) return '-';
        
        const parentCategory = categories.find(cat => cat.id === parentId);
        return parentCategory ? parentCategory.name : '-';
    }

    // 将嵌套的分类树扁平化为一维数组
    function flattenCategoryTree(categoriesData) {
        const result = [];

        if (!categoriesData || !Array.isArray(categoriesData)) {
            return [];
        }

        // 处理嵌套结构
        categoriesData.forEach(category => {
            // 添加一级分类
            result.push({
                id: category.id,
                name: category.name,
                level: parseInt(category.level) || 1,
                image: category.image || '',
                parent_id: category.parent_id || '',
                created_at: category.created_at,
                updated_at: category.updated_at
            });

            // 添加二级分类（如果有）
            if (category.children && Array.isArray(category.children) && category.children.length > 0) {
                category.children.forEach(childCategory => {
                    result.push({
                        id: childCategory.id,
                        name: childCategory.name,
                        level: parseInt(childCategory.level) || 2,
                        image: childCategory.image || '',
                        parent_id: childCategory.parent_id || category.id,
                        created_at: childCategory.created_at,
                        updated_at: childCategory.updated_at
                    });
                });
            }
        });

        return result;
    }

    // 获取分类列表
    function fetchCategories() {

        const loadingIndicator = document.getElementById('loading-indicator');
        const refreshBtn = document.getElementById('refresh-btn');
        const tableBody = document.getElementById('category-table-body');

        // 显示加载状态
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="6">
                        <div class="loading">
                            <div class="spinner"></div>
                            <p style="margin-top: 15px; color: var(--primary-dark);">正在加载分类数据...</p>
                        </div>
                    </td>
                </tr>
            `;
        }

        if (refreshBtn) {
            // 禁用刷新按钮，显示加载状态
            refreshBtn.setAttribute('disabled', 'disabled');
            refreshBtn.innerHTML = '<i class="fas fa-sync fa-spin"></i>';
        }

        // 初始化最大重试次数
        const maxRetries = 3;
        let retryCount = 0;

        // 定义重试函数
        function tryFetchCategories() {
            retryCount++;

            // 直接使用fetch API而不是sendApiRequest包装，直接请求不走加密通道
            fetch('/api/get_category_list')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误: ${response.status}`);
                    }
                    return response.json();
                })
                .then(response => {

                    // 检查响应是否成功
                    if (response.code === 200) {
                        // 保存原始的嵌套数据结构，供其他函数使用
                        window.categoriesOriginalData = response.data;
                        
                        // 渲染分类表格 - 直接传递嵌套结构的数据
                        renderCategoryTable(response.data);
                        
                        // 分析分类数据并显示提示 - 已移除
                        /*
                        setTimeout(() => {
                            if (response.data && response.data.length > 0) {
                                // 检查是否有二级分类
                                const hasSubcategories = response.data.some(cat => 
                                    cat.children && cat.children.length > 0
                                );
                                
                                if (hasSubcategories) {
                                    // 显示提示如何展开二级分类
                                    showNotification('提示：点击ID列前的箭头图标可以展开/折叠二级分类', 'info', 8000);
                                }
                            }
                        }, 1000);
                        */
                    } else {
                        throw new Error(response.msg || '获取分类列表失败');
                    }
                })
                .catch(error => {
                    
                    // 如果还有重试次数，则重试
                    if (retryCount < maxRetries) {
                        setTimeout(tryFetchCategories, 500 * retryCount);
                        return;
                    }
                    
                    showNotification('获取分类列表失败: ' + (error.message || '未知错误'), 'error');

                    if (tableBody) {
                        tableBody.innerHTML = `
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="error-message">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <p>获取分类数据失败</p>
                                        <small>${error.message || '连接服务器出错，请稍后再试'}</small>
                                        <button id="retry-fetch-btn" class="btn btn-outline-primary mt-3">
                                            <i class="fas fa-redo"></i> 重试
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `;

                        // 绑定重试按钮
                        const retryBtn = document.getElementById('retry-fetch-btn');
                        if (retryBtn) {
                            retryBtn.addEventListener('click', fetchCategories);
                        }
                    }
                })
                .finally(() => {
                    // 恢复刷新按钮
                    if (refreshBtn) {
                        refreshBtn.removeAttribute('disabled');
                        refreshBtn.innerHTML = '<i class="fas fa-sync"></i> 刷新';
                    }
                });
        }

        // 开始第一次尝试
        tryFetchCategories();
    }

    // 根据ID在分类树中查找分类（包括所有子分类）
    function findCategoryById(id) {
        return categories.find(category => category.id === id) || null;
    }

    // 更新分类计数
    function updateCategoryCount() {
        const countElement = document.getElementById('category-count');
        if (countElement) {
            const totalCount = categories.length;
            const parentCount = categories.filter(c => parseInt(c.level) === 1).length;
            const childCount = categories.filter(c => parseInt(c.level) === 2).length;

            countElement.textContent = `共 ${totalCount} 个分类（${parentCount} 个一级分类，${childCount} 个二级分类）`;
        }
    }

    // 排序分类，一级分类在前，二级分类跟随父分类
    function sortCategories(categories) {
        // 深拷贝分类数组，避免修改原始数据
        const categoriesCopy = JSON.parse(JSON.stringify(categories));

        // 分离一级和二级分类
        const level1Categories = categoriesCopy.filter(cat => parseInt(cat.level) === 1);
        const level2Categories = categoriesCopy.filter(cat => parseInt(cat.level) === 2);

        // 排序结果数组
        const result = [];

        // 先添加所有一级分类
        result.push(...level1Categories);

        // 然后对于每个一级分类，添加其下的二级分类
        level1Categories.forEach(parent => {
            const children = level2Categories.filter(child => child.parent_id === parent.id);
            result.push(...children);
        });

        // 最后添加未找到父分类的二级分类（如果有）
        const orphanChildren = level2Categories.filter(
            child => !level1Categories.some(parent => parent.id === child.parent_id)
        );
        result.push(...orphanChildren);

        return result;
    }

    // 查找父分类名称
    function findParentCategoryName(parentId) {
        const parent = categories.find(cat => cat.id === parentId);
        return parent ? parent.name : '未知分类';
    }

    // 显示空状态
    function showEmptyState() {
        const tableBody = document.getElementById('category-table-body');
        if (!tableBody) return;

        tableBody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-5">
                    <div class="empty-state">
                        <i class="fas fa-folder-open"></i>
                        <p>暂无分类数据</p>
                        <small>点击"添加分类"按钮创建您的第一个分类</small>
                    </div>
                </td>
            </tr>
        `;
    }

    // 执行保存分类的API请求（新增或更新）
    async function saveCategory(event) {
        if (event) {
            event.preventDefault();
        }

        const saveBtn = document.getElementById('save-btn');
        if (saveBtn) {
            saveBtn.disabled = true;
            saveBtn.textContent = '保存中...';
        }

        const categoryIdElement = document.getElementById('category-id');
        const categoryNameElement = document.getElementById('category-name');
        // 修改这里：从select元素获取值，而不是从input元素
        const categoryLevelElement = document.getElementById('category-level');
        const categoryImage = document.getElementById('category-image');

        // 检查必要元素是否存在
        if (!categoryNameElement) {
            showNotification('保存失败：表单元素缺失', 'error');
            if (saveBtn) {
                saveBtn.disabled = false;
                saveBtn.textContent = '保存';
            }
            return;
        }

        // 获取元素值，确保元素存在后再访问其value属性
        const categoryId = categoryIdElement ? categoryIdElement.value : '';
        const categoryName = categoryNameElement.value;

        // 确保categoryLevelElement存在
        if (!categoryLevelElement) {
            showNotification('保存失败：表单元素缺失', 'error');
            if (saveBtn) {
                saveBtn.disabled = false;
                saveBtn.textContent = '保存';
            }
            return;
        }

        const categoryLevel = categoryLevelElement.value;

        // 验证输入
        if (!categoryName.trim()) {
            showNotification('请输入分类名称', 'warning');
            if (saveBtn) {
                saveBtn.disabled = false;
                saveBtn.textContent = '保存';
            }
            return;
        }

        // 判断操作类型
        const isUpdateOperation = categoryId ? true : false;

        // 准备请求数据
        const requestData = {
            name: categoryName.trim(),
            level: parseInt(categoryLevel)
        };

        // 如果是更新操作，添加ID字段
        if (isUpdateOperation) {
            requestData.id = categoryId;
        }

        // 处理二级分类的父分类
        if (parseInt(categoryLevel) === 2) {
            const parentCategorySelect = document.getElementById('parent-category');
            if (!parentCategorySelect || !parentCategorySelect.value) {
                showNotification('请选择父级分类', 'warning');
                if (saveBtn) {
                    saveBtn.disabled = false;
                    saveBtn.textContent = '保存';
                }
                return;
            }

            requestData.parent_id = parentCategorySelect.value;
        }

        // 设置图片URL - 添加调试日志
        console.log('saveCategory: 获取图片URL元素:', categoryImage);
        console.log('saveCategory: 图片URL值:', categoryImage ? categoryImage.value : 'null');
        
        if (categoryImage && categoryImage.value && categoryImage.value.trim()) {
            requestData.image = categoryImage.value.trim();
            console.log('saveCategory: 设置图片URL为:', requestData.image);
        } else {
            console.log('saveCategory: 未设置图片URL');
        }


        // 判断操作类型：新增或更新
        const endpoint = isUpdateOperation ? 'UpdateCategory' : 'AddCategory';

        // 发送API请求
        sendApiRequest(endpoint, requestData, 'POST')
            .then(response => {

                if (response.code === 200) {
                    showNotification(isUpdateOperation ? '分类更新成功' : '分类添加成功', 'success');
                    // 关闭模态框
                    const categoryModal = document.getElementById('category-modal');
                    if (categoryModal) {
                        closeModal(categoryModal);
                    }
                    // 重新获取分类列表
                    fetchCategories();
                } else {
                    showNotification(`保存失败: ${response.msg || '未知错误'}`, 'error');
                }
            })
            .catch(error => {
                showNotification('保存请求失败: ' + (error.message || '未知错误'), 'error');
            })
            .finally(() => {
                // 恢复按钮状态
                if (saveBtn) {
                    saveBtn.disabled = false;
                    saveBtn.textContent = '保存';
                }
            });
    }

    // 强制在页面加载后立即执行fetchCategories
    window.addEventListener('load', function () {
        fetchCategories();
    });

    // 添加页面可见性监听，确保页面激活时获取最新数据
    document.addEventListener('visibilitychange', function () {
        if (!document.hidden) {
            fetchCategories();
        }
    });

    // 初始化事件监听器
    function initializeEventListeners() {

        // 添加分类按钮点击事件
        const addCategoryBtn = document.getElementById('addCategoryBtn');
        if (addCategoryBtn) {
            // 使用克隆节点替换原按钮，确保之前的事件监听器被移除
            const newBtn = addCategoryBtn.cloneNode(true);
            addCategoryBtn.parentNode.replaceChild(newBtn, addCategoryBtn);
            
            newBtn.addEventListener('click', function (e) {
                e.preventDefault();
                e.stopPropagation();
                openAddCategoryModal();
            });
        } else {
        }

        // 刷新按钮点击事件
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            // 使用克隆节点替换原按钮，确保之前的事件监听器被移除
            const newBtn = refreshBtn.cloneNode(true);
            refreshBtn.parentNode.replaceChild(newBtn, refreshBtn);
            
            newBtn.addEventListener('click', function (e) {
                e.preventDefault();
                e.stopPropagation();
                fetchCategories();
            });
        } else {
        }
        
        // 绑定分类层级选择事件
        const categoryLevelSelect = document.getElementById('category-level');
        if (categoryLevelSelect) {
            categoryLevelSelect.addEventListener('change', function() {
                const parentCategoryGroup = document.getElementById('parent-category-group');
                if (this.value === '2') {
                    // 如果选择二级分类，显示父分类选择器并填充数据
                    if (parentCategoryGroup) {
                        parentCategoryGroup.style.display = 'block';
                        populateParentCategories();
                    }
                } else {
                    // 如果选择一级分类，隐藏父分类选择器
                    if (parentCategoryGroup) {
                        parentCategoryGroup.style.display = 'none';
                    }
                }
            });
        }

        // 初始绑定表格操作按钮（在页面加载后）
        setTimeout(bindTableActions, 1000);
        
        // 图片预览事件
        const imageUrlInput = document.getElementById('category-image');
        const imagePreview = document.getElementById('image-preview');
        if (imageUrlInput && imagePreview) {
            // 移除原有事件监听器
            const newInput = imageUrlInput.cloneNode(true);
            imageUrlInput.parentNode.replaceChild(newInput, imageUrlInput);
            
            newInput.addEventListener('input', function () {
                if (this.value) {
                    imagePreview.src = this.value;
                    imagePreview.classList.add('active');
                } else {
                    imagePreview.src = '';
                    imagePreview.classList.remove('active');
                }
            });
        }

        // 文件上传事件 - 防止重复绑定
        const imageUploadInput = document.getElementById('image-upload');
        if (imageUploadInput && imagePreview && imageUrlInput) {
            // 移除可能存在的旧事件监听器
            const oldHandler = imageUploadInput._categoryImageUploadHandler;
            if (oldHandler) {
                imageUploadInput.removeEventListener('change', oldHandler);
            }
            
            // 创建新的事件处理函数
            const newHandler = function(e) {
                const file = e.target.files[0];
                if (file) {
                    console.log('分类页面选择了图片文件:', file.name, '大小:', file.size);
                    
                    // 检查文件类型
                    if (!file.type.startsWith('image/')) {
                        showNotification('请选择图片文件', 'error');
                        // 重置文件输入框
                        this.value = '';
                        return;
                    }

                    // 检查文件大小 (5MB)
                    if (file.size > 5 * 1024 * 1024) {
                        showNotification('图片文件过大，请选择小于5MB的图片', 'error');
                        // 重置文件输入框
                        this.value = '';
                        return;
                    }

                    // 显示上传中状态
                    const imagePreviewContainer = document.getElementById('image-preview-container');
                    if (imagePreviewContainer) {
                        imagePreviewContainer.innerHTML = `
                            <div style="text-align: center; padding: 20px;">
                                <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: var(--primary-color);"></i>
                                <div style="margin-top: 10px; color: var(--text-secondary);">正在上传图片...</div>
                            </div>
                        `;
                    }

                    // 读取文件并上传
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const base64Data = e.target.result;
                        console.log('分类页面文件读取完成，开始上传');
                        
                        // 上传到服务器
                        uploadCategoryImageToServer(base64Data)
                            .then(imageUrl => {
                                console.log('分类图片上传成功:', imageUrl);

                                // 重新获取DOM元素引用，确保操作的是当前页面中实际存在的元素
                                const currentImageUrlInput = document.getElementById('category-image');
                                const currentImagePreview = document.getElementById('image-preview');

                                console.log('更新图片URL输入框元素:', currentImageUrlInput);
                                console.log('更新前输入框值:', currentImageUrlInput ? currentImageUrlInput.value : 'null');

                                // 上传成功，更新预览和URL输入框
                                if (currentImagePreview) {
                                    currentImagePreview.src = imageUrl;
                                    currentImagePreview.classList.add('active');
                                }

                                if (currentImageUrlInput) {
                                    currentImageUrlInput.value = imageUrl;
                                    console.log('更新后输入框值:', currentImageUrlInput.value);
                                } else {
                                    console.error('无法找到category-image输入框元素');
                                }

                                // 恢复预览容器
                                if (imagePreviewContainer) {
                                    imagePreviewContainer.innerHTML = '<img id="image-preview" class="image-preview active" src="' + imageUrl + '" alt="分类图片预览">';
                                }

                                showNotification('图片上传成功', 'success');
                            })
                            .catch(error => {
                                console.error('分类图片上传失败:', error);
                                
                                // 上传失败，显示错误信息
                                if (imagePreviewContainer) {
                                    imagePreviewContainer.innerHTML = `
                                        <div style="text-align: center; padding: 20px; color: #f44336;">
                                            <i class="fas fa-exclamation-triangle" style="font-size: 24px;"></i>
                                            <div style="margin-top: 10px;">图片上传失败</div>
                                            <div style="margin-top: 5px; font-size: 12px; color: #999;">${error.message}</div>
                                        </div>
                                    `;
                                }
                                showNotification(error.message || '图片上传失败', 'error');
                                
                                // 重置文件输入框
                                imageUploadInput.value = '';
                            });
                    };
                    
                    // 添加文件读取错误处理
                    reader.onerror = function(error) {
                        console.error('文件读取失败:', error);
                        
                        const imagePreviewContainer = document.getElementById('image-preview-container');
                        if (imagePreviewContainer) {
                            imagePreviewContainer.innerHTML = `
                                <div style="text-align: center; padding: 20px; color: #f44336;">
                                    <i class="fas fa-exclamation-triangle" style="font-size: 24px;"></i>
                                    <div style="margin-top: 10px;">文件读取失败</div>
                                </div>
                            `;
                        }
                        
                        showNotification('文件读取失败，请重新选择', 'error');
                        // 重置文件输入框
                        imageUploadInput.value = '';
                    };
                    
                    reader.readAsDataURL(file);
                }
            };
            
            // 绑定新的事件处理器并存储引用
            imageUploadInput.addEventListener('change', newHandler);
            imageUploadInput._categoryImageUploadHandler = newHandler;
        }

        // 绑定模态框事件（最重要的部分）
        bindModalEvents();

        console.log('所有事件监听器初始化完成');
    }
    
    /**
     * 绑定模态框事件 - 重构版本
     * 使用更可靠的事件绑定机制，确保保存按钮点击事件能正确触发
     */
    function bindModalEvents() {
        console.log('开始绑定模态框事件 - 重构版本');

        // 直接绑定保存按钮事件，使用ID选择器确保准确性
        bindSaveButtonEvent();

        // 绑定取消和关闭按钮事件
        bindCloseButtonEvents();

        // 绑定模态框背景点击关闭事件
        bindModalBackgroundEvents();

        console.log('模态框事件绑定完成');
    }

    /**
     * 绑定保存按钮事件 - 核心修复
     */
    function bindSaveButtonEvent() {
        const saveBtn = document.getElementById('save-btn');

        if (!saveBtn) {
            console.warn('保存按钮未找到 (ID: save-btn)');
            return;
        }

        console.log('找到保存按钮:', saveBtn);
        console.log('保存按钮详情:', {
            id: saveBtn.id,
            className: saveBtn.className,
            disabled: saveBtn.disabled,
            visible: saveBtn.offsetParent !== null,
            innerHTML: saveBtn.innerHTML
        });

        // 移除可能存在的旧事件监听器
        const newSaveBtn = saveBtn.cloneNode(true);
        saveBtn.parentNode.replaceChild(newSaveBtn, saveBtn);

        // 绑定新的点击事件
        newSaveBtn.addEventListener('click', function(event) {
            console.log('=== 保存按钮点击事件触发 ===');
            console.log('事件对象:', event);
            console.log('目标元素:', event.target);
            console.log('当前元素:', event.currentTarget);

            // 阻止默认行为和事件冒泡
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();

            // 检查按钮是否被禁用
            if (this.disabled) {
                console.log('保存按钮已禁用，忽略点击事件');
                return;
            }

            // 获取模态框和操作类型
            const categoryModal = document.getElementById('category-modal');
            if (!categoryModal) {
                console.error('分类模态框未找到');
                return;
            }

            const actionType = categoryModal.dataset.action || 'add';
            console.log('保存按钮被点击 - 操作类型:', actionType);

            // 根据操作类型调用相应的函数
            try {
                if (actionType === 'add' || actionType === 'addSubcategory') {
                    console.log('调用addCategory函数');
                    addCategory();
                } else if (actionType === 'update') {
                    console.log('调用updateCategory函数');
                    updateCategory();
                } else {
                    console.log('未知的操作类型，默认调用addCategory:', actionType);
                    addCategory();
                }
            } catch (error) {
                console.error('保存操作执行失败:', error);
                showNotification('保存操作失败: ' + error.message, 'error');
            }
        });

        // 添加其他事件用于调试
        newSaveBtn.addEventListener('mousedown', function() {
            console.log('保存按钮 mousedown 事件');
        });

        newSaveBtn.addEventListener('mouseup', function() {
            console.log('保存按钮 mouseup 事件');
        });

        console.log('保存按钮事件绑定成功');
    }

    /**
     * 绑定关闭按钮事件
     */
    function bindCloseButtonEvents() {
        // 绑定取消按钮
        const cancelBtn = document.getElementById('cancel-btn');
        if (cancelBtn) {
            const newCancelBtn = cancelBtn.cloneNode(true);
            cancelBtn.parentNode.replaceChild(newCancelBtn, cancelBtn);

            newCancelBtn.addEventListener('click', function(event) {
                event.preventDefault();
                event.stopPropagation();
                console.log('取消按钮被点击');

                const categoryModal = document.getElementById('category-modal');
                if (categoryModal) {
                    closeModal(categoryModal);
                }
            });

            console.log('取消按钮事件绑定成功');
        }

        // 绑定模态框关闭按钮
        const modalCloseButtons = document.querySelectorAll('.modal-close');
        modalCloseButtons.forEach(button => {
            button.addEventListener('click', function(event) {
                event.preventDefault();
                event.stopPropagation();
                console.log('模态框关闭按钮被点击');

                const modal = this.closest('.modal-overlay');
                if (modal) {
                    closeModal(modal);
                }
            });
        });

        console.log('关闭按钮事件绑定成功');
    }

    /**
     * 绑定模态框背景点击关闭事件
     */
    function bindModalBackgroundEvents() {
        const modalOverlays = document.querySelectorAll('.modal-overlay');
        modalOverlays.forEach(overlay => {
            overlay.addEventListener('click', function(event) {
                // 只有点击背景时才关闭模态框
                if (event.target === this) {
                    console.log('模态框背景被点击，关闭模态框');
                    closeModal(this);
                }
            });
        });

        console.log('模态框背景事件绑定成功');
    }


    
    // 添加分类
    async function addCategory() {
        try {
            
            // 获取保存按钮并显示加载状态
            const saveBtn = document.getElementById('save-btn');
            if (!saveBtn) {
                return;
            }
            
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
            
            // 获取表单数据 - 添加调试日志
            const categoryName = document.getElementById('category-name').value.trim();
            const categoryLevel = document.getElementById('category-level').value;
            const categoryImageElement = document.getElementById('category-image');
            const categoryImage = categoryImageElement ? categoryImageElement.value.trim() : '';
            
            console.log('addCategory: 获取图片URL元素:', categoryImageElement);
            console.log('addCategory: 图片URL值:', categoryImage);
            
            // 验证表单
            if (!categoryName) {
                showNotification('分类名称不能为空', 'error');
                saveBtn.disabled = false;
                saveBtn.textContent = '保存';
                return;
            }
            
            // 准备请求数据
            let requestData = {
                name: categoryName,
                level: parseInt(categoryLevel),
                image: categoryImage,
                timestamp: new Date().getTime() // 添加时间戳防止缓存
            };
            
            // 如果是子分类，获取父分类ID
            if (categoryLevel === '2') {
                const parentId = document.getElementById('parent-category').value;
                if (!parentId || parentId === '0') {
                    showNotification('请选择父分类', 'error');
                    saveBtn.disabled = false;
                    saveBtn.textContent = '保存';
                    return;
                }
                requestData.parent_id = parentId;
            }
            
            
            // 发送API请求 - 使用与其他API调用一致的方式
            sendApiRequest('AddCategory', requestData, 'POST')
                .then(response => {
                    
                    if (response.code === 200) {
                        // 关闭模态框并刷新分类列表
                        const modal = document.getElementById('category-modal');
                        closeModal(modal);
                        
                        // 显示成功通知
                        showNotification('分类添加成功', 'success');
                        
                        // 重新加载分类列表
                        fetchCategories();
                    } else {
                        showNotification(response.msg || '添加分类失败', 'error');
                    }
                })
                .catch(error => {
                    showNotification(error.message || '添加分类失败，请稍后重试', 'error');
                })
                .finally(() => {
                    // 恢复保存按钮状态
                    saveBtn.disabled = false;
                    saveBtn.textContent = '保存';
                });
        } catch (error) {
            showNotification('添加分类失败: ' + error.message, 'error');
            
            // 恢复保存按钮状态
            const saveBtn = document.getElementById('save-btn');
            if (saveBtn) {
                saveBtn.disabled = false;
                saveBtn.textContent = '保存';
            }
        }
    }

    // 打开编辑分类模态框
    function openEditCategoryModal(category) {

        if (!category || !category.id) {
            showNotification('编辑失败：无效的分类数据', 'error');
            return;
        }

        // 规范化category对象，处理可能的字段差异
        const normalizedCategory = {
            id: category.id,
            name: category.name,
            // 处理level字段可能的情况：直接数值、字符串、对象属性等
            level: parseInt(category.level || category.Level || 1),
            // 处理parent_id字段可能的情况
            parent_id: category.parent_id || category.parentId || category.ParentCategory || '',
            // 处理image字段可能的情况
            image: category.image || category.image_url || category.url || ''
        };

        // 检查如果level不正确，尝试从DOM获取
        if (normalizedCategory.level !== 2 && category.parent_id) {
            normalizedCategory.level = 2;
        }


        const modalTitle = document.querySelector('.modal-title');
        const categoryIdInput = document.getElementById('category-id');
        const parentIdInput = document.getElementById('parent-id');
        const categoryNameInput = document.getElementById('category-name');
        const categoryLevelSelect = document.getElementById('category-level');
        const parentCategoryGroup = document.getElementById('parent-category-group');
        const parentCategorySelect = document.getElementById('parent-category');
        const categoryImageInput = document.getElementById('category-image');
        const imagePreview = document.getElementById('image-preview');
        const levelGroup = document.getElementById('level-group');
        const categoryModal = document.getElementById('category-modal');

        if (!modalTitle || !categoryIdInput || !categoryModal) {
            return;
        }

        // 设置模态框为更新操作
        categoryModal.dataset.action = 'update';
        console.log('设置编辑模态框action为update');

        modalTitle.innerHTML = '<i class="fas fa-edit"></i> 编辑分类';

        if (categoryIdInput) categoryIdInput.value = normalizedCategory.id;
        if (categoryNameInput) categoryNameInput.value = normalizedCategory.name;

        // 设置分类级别并确保选择器可用状态重置
        if (categoryLevelSelect) {
            categoryLevelSelect.value = normalizedCategory.level;
            categoryLevelSelect.disabled = true; // 编辑时禁用级别选择
        }

        if (categoryImageInput) categoryImageInput.value = normalizedCategory.image;


        // 清空原有选择
        if (parentCategorySelect) {
            parentCategorySelect.innerHTML = '';
            parentCategorySelect.disabled = false;
        }

        if (normalizedCategory.level === 2) {

            // 确保二级分类编辑时，显示父级分类选择器
            if (parentIdInput) {
                parentIdInput.value = normalizedCategory.parent_id;
            }

            // 确保父分类组显示 - 直接强制应用内联样式
            if (parentCategoryGroup) {
                // 使用多种方法确保元素显示
                parentCategoryGroup.style.cssText = "display: block !important; visibility: visible !important; opacity: 1 !important;";
                parentCategoryGroup.setAttribute('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');

                // 填充可选的父分类
                if (parentCategorySelect) {

                    // 获取所有一级分类
                    const parentCategories = categories.filter(cat => {
                        // 规范化比较，确保正确识别一级分类
                        const catLevel = parseInt(cat.level || cat.Level || 1);
                        return catLevel === 1 && cat.id !== normalizedCategory.id;
                    });


                    // 添加选项
                    parentCategories.forEach(parentCat => {
                        const option = document.createElement('option');
                        option.value = parentCat.id;
                        option.textContent = parentCat.name;
                        parentCategorySelect.appendChild(option);
                    });

                    // 选中当前父分类
                    if (normalizedCategory.parent_id) {
                        parentCategorySelect.value = normalizedCategory.parent_id;
                    }

                    // 确保下拉框也是可见的
                    parentCategorySelect.style.cssText = "display: block !important;";

                    // 添加一个说明标签
                    const parentCategoryLabel = document.querySelector('label[for="parent-category"]');
                    if (parentCategoryLabel) {
                        parentCategoryLabel.innerHTML = '<i class="fas fa-sitemap" style="color: var(--primary-color); margin-right: 8px;"></i>父级分类 <small style="font-weight: normal; color: #ff7eb9;">(可更改)</small>';
                        parentCategoryLabel.style.cssText = "display: block !important;";
                    }
                } else {
                }
            } else {
            }

            // 编辑二级分类时隐藏层级选择
            if (levelGroup) {
                levelGroup.style.display = 'none';
            }
        } else {
            // 一级分类不显示父级选择器
            if (parentIdInput) parentIdInput.value = '';
            if (parentCategoryGroup) parentCategoryGroup.style.display = 'none';
            if (levelGroup) levelGroup.style.display = 'none'; // 编辑时不允许更改层级
        }

        if (imagePreview && normalizedCategory.image) {
            imagePreview.src = normalizedCategory.image;
            imagePreview.classList.add('active');
        } else if (imagePreview) {
            imagePreview.classList.remove('active');
        }

        openModal(categoryModal);

        // 聚焦到分类名称输入框
        if (categoryNameInput) {
            setTimeout(() => categoryNameInput.focus(), 300);
        }
    }
    
    // 直接初始化
    document.addEventListener('DOMContentLoaded', function() {
        initializeEventListeners();
        fetchCategories();
    });
    
    // 确保立即执行初始化，以防DOMContentLoaded已经触发过
    (function() {
        // 检查DOM是否已经加载完成
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            // DOM已加载，直接初始化
            setTimeout(function() {
                initializeEventListeners();
                fetchCategories();
            }, 100);
        }
    })();

    // 执行删除分类
    async function executeDeleteCategory() {

        const confirmDialog = document.getElementById('confirm-dialog');
        if (!confirmDialog) {
            return;
        }

        const categoryId = confirmDialog.getAttribute('data-category-id');
        if (!categoryId) {
            showNotification('错误：无法确定要删除的分类', 'error');
            closeModal(confirmDialog);
            return;
        }


        // 禁用确认按钮，防止重复点击
        const confirmBtn = document.getElementById('confirm-delete-btn');
        if (confirmBtn) {
            confirmBtn.disabled = true;
            confirmBtn.textContent = '删除中...';
        }

        // 准备请求数据
        const requestData = {
            id: categoryId
        };

        // 发送删除请求
        sendApiRequest('DeleteCategory', requestData, 'POST')
            .then(response => {

                if (response.code === 200) {
                    showNotification('分类删除成功', 'success');
                    // 重新获取分类列表
                    fetchCategories();
                } else {
                    showNotification('删除失败: ' + (response.msg || '未知错误'), 'error');
                }
            })
            .catch(error => {
                showNotification('删除请求失败', 'error');
            })
            .finally(() => {
                // 恢复按钮状态
                if (confirmBtn) {
                    confirmBtn.disabled = false;
                    confirmBtn.textContent = '确认删除';
                }
                // 关闭确认对话框
                closeModal(confirmDialog);
            });
    }

    // 切换子分类显示/隐藏
    function toggleSubcategories(categoryId) {
        const childRows = document.querySelectorAll(`.subcategory-${categoryId}`);
        const toggleIcon = document.querySelector(`.toggle-icon[data-id="${categoryId}"]`);

        // 检查第一个子分类是否可见，据此判断当前状态
        const isCurrentlyCollapsed = childRows.length > 0 &&
            childRows[0].style.display === 'none';


        // 根据当前状态执行相反的操作
        if (isCurrentlyCollapsed) {
            // 如果当前是折叠状态，则展开
            childRows.forEach(row => {
                row.style.display = '';
            });
            // 更改图标为展开状态
            if (toggleIcon) toggleIcon.style.transform = 'rotate(0deg)';
        } else {
            // 如果当前是展开状态，则折叠
            childRows.forEach(row => {
                row.style.display = 'none';
            });
            // 更改图标为折叠状态
            if (toggleIcon) toggleIcon.style.transform = 'rotate(-90deg)';
        }
    }

    // 处理分类层级变化
    function handleLevelChange() {
        if (this.value === '2') {
            const parentCategoryGroup = document.getElementById('parent-category-group');
            if (parentCategoryGroup) {
                parentCategoryGroup.style.display = 'block';
                populateParentCategories();
            }
        } else {
            const parentCategoryGroup = document.getElementById('parent-category-group');
            if (parentCategoryGroup) {
                parentCategoryGroup.style.display = 'none';
            }
        }
    }

    // 填充父级分类选择下拉框
    function populateParentCategories() {
        const parentCategorySelect = document.getElementById('parent-category');
        if (!parentCategorySelect) return;

        parentCategorySelect.innerHTML = '';

        const parentCategories = categories.filter(cat => parseInt(cat.level || cat.Level || 1) === 1);

        parentCategories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name || '未命名分类';
            parentCategorySelect.appendChild(option);
        });

        // 默认选中第一个选项
        if (parentCategorySelect.options.length > 0) {
            parentCategorySelect.selectedIndex = 0;
        }
    }

    // 打开添加分类模态框
    function openAddCategoryModal() {
        const modal = document.getElementById('category-modal');
        if (!modal) {
            return;
        }
        
        
        // 设置模态框为添加操作
        modal.dataset.action = 'add';
        console.log('设置添加分类模态框action为add');
        
        // 重置表单
        const form = document.getElementById('category-form');
        if (form) {
            form.reset();
        }
        
        // 清除隐藏字段和输入字段值
        const categoryIdInput = document.getElementById('category-id');
        if (categoryIdInput) categoryIdInput.value = '';
        
        const parentIdInput = document.getElementById('parent-id');
        if (parentIdInput) parentIdInput.value = '';
        
        const categoryNameInput = document.getElementById('category-name');
        if (categoryNameInput) categoryNameInput.value = '';
        
        const categoryImageInput = document.getElementById('category-image');
        if (categoryImageInput) categoryImageInput.value = '';
        
        // 默认设置为一级分类
        const categoryLevelSelect = document.getElementById('category-level');
        if (categoryLevelSelect) {
            categoryLevelSelect.value = '1';
            categoryLevelSelect.disabled = false;
        }
        
        // 隐藏父分类选择区域
        const parentCategoryGroup = document.getElementById('parent-category-group');
        if (parentCategoryGroup) {
            parentCategoryGroup.style.display = 'none';
        }
        
        // 显示层级选择区域
        const levelGroup = document.getElementById('level-group');
        if (levelGroup) {
            levelGroup.style.display = 'block';
        }
        
        // 设置模态框标题
        const modalTitle = modal.querySelector('.modal-title');
        if (modalTitle) {
            modalTitle.textContent = '添加分类';
        }
        
        // 重置图片预览
        const imagePreview = document.getElementById('image-preview');
        if (imagePreview) {
            imagePreview.src = '';
            imagePreview.classList.remove('active');
        }
        
        // 打开模态框
        openModal(modal);
    }
    
    // 打开添加子分类模态框
    function openAddSubcategoryModal(parentId) {

        const modalTitle = document.querySelector('.modal-title');
        const categoryIdInput = document.getElementById('category-id');
        const parentIdInput = document.getElementById('parent-id');
        const categoryNameInput = document.getElementById('category-name');
        const categoryLevelSelect = document.getElementById('category-level');
        const parentCategoryGroup = document.getElementById('parent-category-group');
        const parentCategorySelect = document.getElementById('parent-category');
        const categoryImageInput = document.getElementById('category-image');
        const imagePreview = document.getElementById('image-preview');
        const levelGroup = document.getElementById('level-group');
        const categoryModal = document.getElementById('category-modal');

        if (!modalTitle || !categoryIdInput || !categoryModal) {
            return;
        }

        // 设置模态框为添加子分类操作
        categoryModal.dataset.action = 'addSubcategory';
        console.log('设置添加子分类模态框action为addSubcategory');

        modalTitle.textContent = '添加子分类';
        categoryIdInput.value = '';
        if (parentIdInput) parentIdInput.value = parentId;
        categoryNameInput.value = '';
        if (categoryLevelSelect) {
            categoryLevelSelect.value = '2';
            categoryLevelSelect.disabled = true; // 禁用切换，固定为二级分类
        }
        if (levelGroup) {
            levelGroup.style.display = 'none'; // 隐藏层级选择组
        }
        if (categoryImageInput) categoryImageInput.value = '';
        if (imagePreview) {
            imagePreview.src = '';
            imagePreview.classList.remove('active');
        }

        // 显示父分类选择器
        if (parentCategoryGroup) {
            parentCategoryGroup.style.display = 'block';

            // 填充父分类选择器选项
            if (parentCategorySelect) {
                parentCategorySelect.innerHTML = '';

                // 获取所有一级分类
                const parentCategories = categories.filter(cat => parseInt(cat.level || cat.Level || 1) === 1);


                // 添加选项
                parentCategories.forEach(parent => {
                    const option = document.createElement('option');
                    option.value = parent.id;
                    option.textContent = parent.name;
                    parentCategorySelect.appendChild(option);
                });

                // 选中指定的父分类
                parentCategorySelect.value = parentId;

                // 禁用选择器，因为添加子分类时不能更改父分类
                parentCategorySelect.disabled = true;
            }
        }

        openModal(categoryModal);

        // 聚焦到分类名称输入框
        if (categoryNameInput) {
            setTimeout(() => categoryNameInput.focus(), 300);
        }
    }

    // 打开删除确认对话框
    function openDeleteConfirmDialog(categoryId, categoryName) {

        // 参数验证
        if (!categoryId || typeof categoryId !== 'string') {
            showNotification('删除失败：无效的分类ID', 'error');
            return;
        }

        const confirmDialog = document.getElementById('confirm-dialog');
        if (!confirmDialog) {
            return;
        }

        const confirmMessage = document.getElementById('confirm-message');
        if (confirmMessage) {
            confirmMessage.textContent = `确定要删除分类 "${categoryName || '未命名分类'}" 吗？`;
        }

        // 存储要删除的分类ID，供后续确认删除使用
        confirmDialog.setAttribute('data-category-id', categoryId);

        openModal(confirmDialog);

        // 直接在这里绑定按钮事件，确保事件被正确绑定
        const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
        const cancelDeleteBtn = document.getElementById('cancel-delete-btn');

        if (confirmDeleteBtn) {
            // 克隆并替换按钮以移除旧事件
            const newConfirmBtn = confirmDeleteBtn.cloneNode(true);
            confirmDeleteBtn.parentNode.replaceChild(newConfirmBtn, confirmDeleteBtn);

            newConfirmBtn.addEventListener('click', function (e) {
                e.preventDefault();
                e.stopPropagation();
                executeDeleteCategory();
            });
        }

        if (cancelDeleteBtn) {
            // 克隆并替换按钮以移除旧事件
            const newCancelBtn = cancelDeleteBtn.cloneNode(true);
            cancelDeleteBtn.parentNode.replaceChild(newCancelBtn, cancelDeleteBtn);

            newCancelBtn.addEventListener('click', function (e) {
                e.preventDefault();
                e.stopPropagation();
                closeModal(confirmDialog);
            });
        }
    }

    // 打开模态框
    function openModal(modal) {
        if (!modal) return;
        
        
        // 显示模态框
        modal.classList.add('active');
        
        // 确保模态框层也添加active类
        const modalOverlay = modal.closest('.modal-overlay');
        if (modalOverlay && !modalOverlay.classList.contains('active')) {
            modalOverlay.classList.add('active');
        }
        
        // 绑定ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal(modal);
            }
        });
        
        // 绑定关闭按钮事件
        const closeButtons = modal.querySelectorAll('.modal-close, .cancel-btn');
        closeButtons.forEach(button => {
            button.onclick = function() {
                closeModal(modal);
            };
        });
    }

    // 关闭模态框的函数
    function closeModal(modal) {
        if (!modal) return;
        
        // 添加退出动画
        const modalContent = modal.querySelector('.modal');
        if (modalContent) {
            modalContent.style.transform = 'translateY(-20px)';
            modalContent.style.opacity = '0';
        }
        
        // 等待动画完成后隐藏模态框
        setTimeout(() => {
            modal.classList.remove('active');
            
            // 重置表单
            const form = modal.querySelector('form');
            if (form) {
                form.reset();
            }
            
            // 重置图片预览
            const imagePreview = modal.querySelector('.image-preview');
            if (imagePreview) {
                imagePreview.classList.remove('active');
            }
            
            // 恢复模态框样式
            if (modalContent) {
                setTimeout(() => {
                    modalContent.style.transform = '';
                    modalContent.style.opacity = '';
                }, 100);
            }
        }, 200);
    }

    // 显示通知的函数
    function showNotification(message, type = 'success', duration = 3000) {
        
        const notification = document.getElementById('notification');
        const messageElement = document.getElementById('notification-message');
        
        if (!notification || !messageElement) {
            alert(message);
            return;
        }
        
        // 设置通知类型和图标
        notification.className = 'notification';
        notification.classList.add(type);
        
        // 设置图标
        const icon = notification.querySelector('i');
        if (icon) {
            icon.className = 'fas';
            switch (type) {
                case 'success':
                    icon.classList.add('fa-check-circle');
                    break;
                case 'error':
                    icon.classList.add('fa-times-circle');
                    break;
                case 'warning':
                    icon.classList.add('fa-exclamation-triangle');
                    break;
                case 'info':
                    icon.classList.add('fa-info-circle');
                    break;
                default:
                    icon.classList.add('fa-bell');
            }
        }
        
        // 设置消息
        messageElement.textContent = message;
        
        // 显示通知
        notification.classList.add('show');
        
        // 添加按钮事件
        const closeBtn = notification.querySelector('.close-notification');
        if (closeBtn) {
            closeBtn.onclick = function() {
                notification.classList.remove('show');
            };
        }
        
        // 自动关闭
        if (duration > 0) {
            setTimeout(() => {
                notification.classList.remove('show');
            }, duration);
        }
    }



    // 初始化函数 - 在页面加载完成后调用
    function init() {
        fetchCategories();
        bindEvents();
    }

    function deleteCategory(categoryId) {
        if (!categoryId) {
            showNotification('分类ID无效', 'error');
            return;
        }
        
        // 显示加载状态
        const confirmDialog = document.getElementById('confirmDialog');
        const confirmButton = confirmDialog.querySelector('.confirm-btn');
        confirmButton.innerHTML = '<div class="spinner"></div> 删除中...';
        confirmButton.disabled = true;
        
        // 准备请求数据
        const requestData = {
            id: categoryId,
            timestamp: Date.now().toString()
        };
        
        // 生成签名
        requestData.sign = generateSignature(requestData);
        
        // 发送删除请求
        fetch('/api/category/delete/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            // 恢复按钮状态
            confirmButton.textContent = '删除';
            confirmButton.disabled = false;
            
            // 关闭确认对话框
            confirmDialog.classList.remove('active');
            
            if (data.code === 0) {
                // 删除成功
                showNotification('分类删除成功', 'success');
                fetchCategories(); // 刷新分类列表
            } else {
                // 删除失败
                showNotification(`删除失败: ${data.msg}`, 'error');
            }
        })
        .catch(error => {
            // 恢复按钮状态
            confirmButton.textContent = '删除';
            confirmButton.disabled = false;
            
            // 关闭确认对话框
            confirmDialog.classList.remove('active');
            
            showNotification('删除失败，请检查网络连接', 'error');
        });
    }

// 页面加载完成后立即执行初始化
    
    // 确保主脚本已加载
    setTimeout(function() {
        if (typeof initializeEventListeners === 'function' && typeof fetchCategories === 'function') {
            initializeEventListeners();
            fetchCategories();
        } else {
        }
    }, 300);

    // 暴露必要的函数到全局作用域，以便其他脚本调用
    window.fetchCategories = fetchCategories;
    window.initializeEventListeners = initializeEventListeners;
    window.openAddCategoryModal = openAddCategoryModal;
    window.editCategory = editCategory;
    window.deleteCategory = deleteCategory;

})(window);